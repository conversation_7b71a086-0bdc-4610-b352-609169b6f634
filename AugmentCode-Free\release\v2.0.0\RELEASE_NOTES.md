# AugmentCode-Free v2.0.0 发布说明

## 🎉 版本亮点

### 🎨 界面优化与多语言完善
- **完美多语言支持**：所有界面元素支持中英文实时切换，包括按钮、标签、下拉框选项
- **布局优化**：窗口宽度增加至680px，按钮宽度全面提升，确保英文文本完整显示
- **界面细节优化**：清空日志按钮定位优化，垂直高度增加，解决显示截断问题
- **用户体验提升**：移除多余emoji，界面更简洁专业

### 🔧 补丁功能集成
- **代码补丁应用**：新增补丁应用、恢复、扫描功能
- **多种补丁模式**：支持随机假数据、完全阻止、空数据、隐身模式、调试模式
- **状态管理**：完善补丁状态检测和管理机制

### 🧹 代码优化
- **项目清理**：删除所有测试文件和无效文档
- **结构优化**：清理临时文件和版本文件
- **文档更新**：完善README.md，添加最新功能说明

## 📦 下载文件

- **源码包**: `AugmentCode-Free-v2.0.0-Source.zip`
- **可执行文件**: `AugmentCode-Free-v2.0.0.exe` (构建中)
- **便携版**: `AugmentCode-Free-v2.0.0-Portable.zip` (构建中)

## 🔧 系统要求

- **操作系统**: Windows 10/11 x64
- **Python**: 3.7+ (源码运行)
- **支持IDE**: VS Code、Cursor、Windsurf、JetBrains 系列

## 🚀 使用方法

1. 下载对应的发布包
2. 解压到任意目录
3. 运行 `AugmentCode-Free-v2.0.0.exe` 或 `python main.py`
4. 选择需要清理的IDE
5. 点击相应的功能按钮

## 📝 更新日志

详见项目 README.md 文件中的更新记录。

---

**发布日期**: 2025年8月13日  
**版本**: v2.0.0  
**作者**: BasicProtein
