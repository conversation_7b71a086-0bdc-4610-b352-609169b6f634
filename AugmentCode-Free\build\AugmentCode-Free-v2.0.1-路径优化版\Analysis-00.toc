(['E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\main.py'],
 ['E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free'],
 [],
 [('D:\\Software\\Python\\Python310\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\Software\\Python\\Python310\\lib\\site-packages\\rapidfuzz\\__pyinstaller',
   0),
  ('D:\\Software\\Python\\Python310\\lib\\site-packages\\soundcard\\__pyinstaller',
   0),
  ('D:\\Software\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Software\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.10.8 (tags/v3.10.8:aaaf517, Oct 11 2022, 16:50:30) [MSC v.1933 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Software\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Software\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Software\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'D:\\Software\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('main',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\main.py',
   'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\Software\\Python\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('contextlib',
   'D:\\Software\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Software\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Software\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Software\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Software\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\Software\\Python\\Python310\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Software\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Software\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Software\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Software\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('copy', 'D:\\Software\\Python\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('random', 'D:\\Software\\Python\\Python310\\lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\Software\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('decimal', 'D:\\Software\\Python\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Software\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Software\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'D:\\Software\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers', 'D:\\Software\\Python\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Software\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\Software\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\Software\\Python\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Software\\Python\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\Software\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect', 'D:\\Software\\Python\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('argparse', 'D:\\Software\\Python\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('shutil', 'D:\\Software\\Python\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Software\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Software\\Python\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\Software\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'D:\\Software\\Python\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Software\\Python\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Software\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('gettext', 'D:\\Software\\Python\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('token', 'D:\\Software\\Python\\Python310\\lib\\token.py', 'PYMODULE'),
  ('dis', 'D:\\Software\\Python\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Software\\Python\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Software\\Python\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Software\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct', 'D:\\Software\\Python\\Python310\\lib\\struct.py', 'PYMODULE'),
  ('threading',
   'D:\\Software\\Python\\Python310\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Software\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('bisect', 'D:\\Software\\Python\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('_strptime',
   'D:\\Software\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE'),
  ('datetime', 'D:\\Software\\Python\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('calendar', 'D:\\Software\\Python\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Software\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Software\\Python\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Software\\Python\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'D:\\Software\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Software\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Software\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Software\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Software\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Software\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Software\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Software\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('socket', 'D:\\Software\\Python\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('selectors',
   'D:\\Software\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE'),
  ('quopri', 'D:\\Software\\Python\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\Software\\Python\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\Software\\Python\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Software\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'D:\\Software\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile',
   'D:\\Software\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('email',
   'D:\\Software\\Python\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Software\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Software\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'D:\\Software\\Python\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('tokenize', 'D:\\Software\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Software\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('signal', 'D:\\Software\\Python\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Software\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Software\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Software\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Software\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Software\\Python\\Python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\Software\\Python\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Software\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Software\\Python\\Python310\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Software\\Python\\Python310\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\Software\\Python\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\Software\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Software\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'D:\\Software\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl', 'D:\\Software\\Python\\Python310\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Software\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('hmac', 'D:\\Software\\Python\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'D:\\Software\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Software\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Software\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Software\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\Software\\Python\\Python310\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Software\\Python\\Python310\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\Software\\Python\\Python310\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Software\\Python\\Python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport',
   'D:\\Software\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Software\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Software\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Software\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('gui_qt6.main_window',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\main_window.py',
   'PYMODULE'),
  ('gui_qt6',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\__init__.py',
   'PYMODULE'),
  ('gui_qt6.about_dialog',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\about_dialog.py',
   'PYMODULE'),
  ('webbrowser',
   'D:\\Software\\Python\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('glob', 'D:\\Software\\Python\\Python310\\lib\\glob.py', 'PYMODULE'),
  ('gui_qt6.font_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\font_manager.py',
   'PYMODULE'),
  ('platform', 'D:\\Software\\Python\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('gui_qt6.styles',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\styles.py',
   'PYMODULE'),
  ('gui_qt6.main_page',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\main_page.py',
   'PYMODULE'),
  ('augment_tools_core.patch_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\patch_manager.py',
   'PYMODULE'),
  ('augment_tools_core',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\__init__.py',
   'PYMODULE'),
  ('augment_tools_core.common_utils',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\common_utils.py',
   'PYMODULE'),
  ('colorama',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('uuid', 'D:\\Software\\Python\\Python310\\lib\\uuid.py', 'PYMODULE'),
  ('gui_qt6.patch_worker',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\patch_worker.py',
   'PYMODULE'),
  ('augment_tools_core.extension_finder',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\extension_finder.py',
   'PYMODULE'),
  ('gui_qt6.workers',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\workers.py',
   'PYMODULE'),
  ('augment_tools_core.process_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\process_manager.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('augment_tools_core.telemetry_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\telemetry_manager.py',
   'PYMODULE'),
  ('augment_tools_core.jetbrains_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\jetbrains_manager.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('json',
   'D:\\Software\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Software\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Software\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Software\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('augment_tools_core.database_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\database_manager.py',
   'PYMODULE'),
  ('augment_tools_core.cleanup_strategies',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\cleanup_strategies.py',
   'PYMODULE'),
  ('augment_tools_core.file_cleaner',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\file_cleaner.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Software\\Python\\Python310\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Software\\Python\\Python310\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Software\\Python\\Python310\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('gui_qt6.components',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\components.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Software\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('gui_qt6.welcome_page',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\welcome_page.py',
   'PYMODULE'),
  ('PyQt6',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('language_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\language_manager.py',
   'PYMODULE'),
  ('config_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\config_manager.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Software\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE')],
 [('python310.dll', 'D:\\Software\\Python\\Python310\\python310.dll', 'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'D:\\Software\\Python\\Python310\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\Software\\Python\\Python310\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Software\\Python\\Python310\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Software\\Python\\Python310\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp310-win_amd64.pyd',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Software\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Software\\Python\\Python310\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Software\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'D:\\Software\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'D:\\Software\\Python\\Python310\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\Software\\Python\\Python310\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Software\\Python\\Python310\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'E:\\Software\\Java\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('base_library.zip',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\build\\AugmentCode-Free-v2.0.1-路径优化版\\base_library.zip',
   'DATA')],
 [('_collections_abc',
   'D:\\Software\\Python\\Python310\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('warnings', 'D:\\Software\\Python\\Python310\\lib\\warnings.py', 'PYMODULE'),
  ('copyreg', 'D:\\Software\\Python\\Python310\\lib\\copyreg.py', 'PYMODULE'),
  ('traceback',
   'D:\\Software\\Python\\Python310\\lib\\traceback.py',
   'PYMODULE'),
  ('collections.abc',
   'D:\\Software\\Python\\Python310\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\Software\\Python\\Python310\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('operator', 'D:\\Software\\Python\\Python310\\lib\\operator.py', 'PYMODULE'),
  ('enum', 'D:\\Software\\Python\\Python310\\lib\\enum.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\Software\\Python\\Python310\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('re', 'D:\\Software\\Python\\Python310\\lib\\re.py', 'PYMODULE'),
  ('io', 'D:\\Software\\Python\\Python310\\lib\\io.py', 'PYMODULE'),
  ('abc', 'D:\\Software\\Python\\Python310\\lib\\abc.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\Software\\Python\\Python310\\lib\\sre_constants.py',
   'PYMODULE'),
  ('genericpath',
   'D:\\Software\\Python\\Python310\\lib\\genericpath.py',
   'PYMODULE'),
  ('reprlib', 'D:\\Software\\Python\\Python310\\lib\\reprlib.py', 'PYMODULE'),
  ('weakref', 'D:\\Software\\Python\\Python310\\lib\\weakref.py', 'PYMODULE'),
  ('heapq', 'D:\\Software\\Python\\Python310\\lib\\heapq.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\Software\\Python\\Python310\\lib\\sre_compile.py',
   'PYMODULE'),
  ('posixpath',
   'D:\\Software\\Python\\Python310\\lib\\posixpath.py',
   'PYMODULE'),
  ('ntpath', 'D:\\Software\\Python\\Python310\\lib\\ntpath.py', 'PYMODULE'),
  ('codecs', 'D:\\Software\\Python\\Python310\\lib\\codecs.py', 'PYMODULE'),
  ('stat', 'D:\\Software\\Python\\Python310\\lib\\stat.py', 'PYMODULE'),
  ('keyword', 'D:\\Software\\Python\\Python310\\lib\\keyword.py', 'PYMODULE'),
  ('sre_parse',
   'D:\\Software\\Python\\Python310\\lib\\sre_parse.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Software\\Python\\Python310\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('linecache',
   'D:\\Software\\Python\\Python310\\lib\\linecache.py',
   'PYMODULE'),
  ('locale', 'D:\\Software\\Python\\Python310\\lib\\locale.py', 'PYMODULE'),
  ('types', 'D:\\Software\\Python\\Python310\\lib\\types.py', 'PYMODULE'),
  ('functools',
   'D:\\Software\\Python\\Python310\\lib\\functools.py',
   'PYMODULE'),
  ('os', 'D:\\Software\\Python\\Python310\\lib\\os.py', 'PYMODULE')])
