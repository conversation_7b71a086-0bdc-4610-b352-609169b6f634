const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

/**
 * VSCode State Database Cleaner
 * 删除VSCode的state.vscdb和state.vscdb.backup文件
 */

// 获取APPDATA路径
const appDataPath = process.env.APPDATA || path.join(os.homedir(), 'AppData', 'Roaming');
const codeUserPath = path.join(appDataPath, 'Code', 'User');

// 定义要删除的文件名
const TARGET_FILES = ['state.vscdb', 'state.vscdb.backup'];

/**
 * 检查VSCode进程是否仍在运行
 * @returns {boolean} - 是否有VSCode进程在运行
 */
function checkVSCodeProcesses() {
    try {
        if (process.platform === 'win32') {
            const result = execSync('tasklist /FI "IMAGENAME eq Code.exe" /FO CSV', { encoding: 'utf8' });
            return result.includes('Code.exe');
        } else {
            const result = execSync('pgrep -f "code|Code"', { encoding: 'utf8' });
            return result.trim().length > 0;
        }
    } catch (error) {
        // pgrep返回1表示没有找到进程，这是正常的
        return false;
    }
}

/**
 * 列出所有VSCode进程
 * @returns {Array} - VSCode进程信息数组
 */
function listVSCodeProcesses() {
    try {
        if (process.platform === 'win32') {
            const result = execSync('tasklist /FI "IMAGENAME eq Code.exe" /FO TABLE', { encoding: 'utf8' });
            const lines = result.split('\n').filter(line => line.includes('Code.exe'));
            return lines.map(line => {
                const parts = line.trim().split(/\s+/);
                return {
                    name: parts[0],
                    pid: parts[1],
                    memory: parts[4] + ' ' + parts[5]
                };
            });
        } else {
            const result = execSync('ps aux | grep -E "code|Code" | grep -v grep', { encoding: 'utf8' });
            return result.trim().split('\n').filter(line => line.trim());
        }
    } catch (error) {
        return [];
    }
}

/**
 * 等待指定的毫秒数
 * @param {number} ms - 等待的毫秒数
 * @returns {Promise} - Promise对象
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 获取所有VSCode相关进程的详细信息
 * @returns {Array} - VSCode进程信息数组
 */
function getAllVSCodeProcesses() {
    try {
        if (process.platform === 'win32') {
            // 获取所有VSCode相关进程，包括主进程和子进程
            const vscodeProcesses = [];

            // 查找所有可能的VSCode相关进程
            const processNames = [
                'Code.exe',
                'Code - Insiders.exe',
                'Code - OSS.exe',
                'VSCode.exe',
                'electron.exe'  // VSCode基于Electron
            ];

            for (const processName of processNames) {
                try {
                    const result = execSync(`tasklist /FI "IMAGENAME eq ${processName}" /FO CSV`, { encoding: 'utf8' });
                    const lines = result.split('\n').filter(line => line.includes(processName));

                    const processes = lines.map(line => {
                        // CSV格式解析
                        const parts = line.replace(/"/g, '').split(',');
                        if (parts.length >= 2) {
                            return {
                                name: parts[0],
                                pid: parts[1],
                                memory: parts.length > 4 ? parts[4] : 'N/A'
                            };
                        }
                        return null;
                    }).filter(proc => proc !== null);

                    vscodeProcesses.push(...processes);
                } catch (error) {
                    // 忽略单个进程名查找失败
                }
            }

            // 额外查找：通过命令行参数查找VSCode进程
            try {
                const wmicResult = execSync('wmic process where "CommandLine like \'%Code%\'" get ProcessId,Name,CommandLine /format:csv', { encoding: 'utf8' });
                const wmicLines = wmicResult.split('\n').filter(line => line.includes('Code') && line.includes('.exe'));

                wmicLines.forEach(line => {
                    const parts = line.split(',');
                    if (parts.length >= 3) {
                        const name = parts[1];
                        const pid = parts[2];
                        if (name && pid && !vscodeProcesses.some(p => p.pid === pid)) {
                            vscodeProcesses.push({
                                name: name,
                                pid: pid,
                                memory: 'N/A'
                            });
                        }
                    }
                });
            } catch (error) {
                // 忽略wmic查找失败
            }

            return vscodeProcesses;
        } else {
            const result = execSync('ps aux | grep -E "code|Code" | grep -v grep', { encoding: 'utf8' });
            return result.trim().split('\n').filter(line => line.trim());
        }
    } catch (error) {
        return [];
    }
}

/**
 * 强制终止所有VSCode进程（增强版）
 * @returns {Promise<boolean>} - 是否成功终止进程
 */
async function killAllVSCodeProcesses() {
    try {
        console.log('[INFO] 正在强制终止所有VSCode进程...');

        // 先获取所有VSCode进程的详细信息
        const processes = getAllVSCodeProcesses();
        if (processes.length === 0) {
            console.log('[INFO] 未找到VSCode进程');
            return true;
        }

        console.log(`[INFO] 找到 ${processes.length} 个VSCode进程:`);
        processes.forEach(proc => {
            if (typeof proc === 'string') {
                console.log(`       ${proc}`);
            } else {
                console.log(`       PID: ${proc.pid}, 内存: ${proc.memory}`);
            }
        });

        // 第一步：使用taskkill /F /IM强制终止所有可能的VSCode进程
        if (process.platform === 'win32') {
            console.log('[INFO] 步骤1: 使用 taskkill /F /IM 终止所有VSCode相关进程...');
            const processNames = [
                'Code.exe',
                'Code - Insiders.exe',
                'Code - OSS.exe',
                'VSCode.exe'
            ];

            for (const processName of processNames) {
                try {
                    execSync(`taskkill /F /IM "${processName}"`, { stdio: 'pipe' });
                    console.log(`[INFO] 已终止 ${processName} 进程`);
                } catch (error) {
                    // 忽略进程不存在的错误
                }
            }

            // 额外终止可能的Electron进程（VSCode基于Electron）
            try {
                const electronResult = execSync('wmic process where "CommandLine like \'%Code%\' and Name=\'electron.exe\'" get ProcessId /format:csv', { encoding: 'utf8' });
                const electronPids = electronResult.split('\n')
                    .filter(line => line.includes(','))
                    .map(line => line.split(',')[1])
                    .filter(pid => pid && pid.trim());

                for (const pid of electronPids) {
                    try {
                        execSync(`taskkill /F /PID ${pid.trim()}`, { stdio: 'pipe' });
                        console.log(`[INFO] 已终止 Electron 进程 PID: ${pid.trim()}`);
                    } catch (error) {
                        // 忽略进程不存在的错误
                    }
                }
            } catch (error) {
                // 忽略wmic查找失败
            }

            console.log('[INFO] taskkill 命令执行完成');
        } else {
            execSync('pkill -f "code|Code"', { stdio: 'ignore' });
        }

        // 等待进程终止
        console.log('[INFO] 等待进程终止...');
        await sleep(3000);

        // 第二步：检查是否还有残留进程，逐个终止
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
            const remainingProcesses = getAllVSCodeProcesses();

            if (remainingProcesses.length === 0) {
                console.log('[SUCCESS] 所有VSCode进程已终止');
                return true;
            }

            console.log(`[INFO] 仍有 ${remainingProcesses.length} 个进程在运行，尝试逐个终止...`);

            // 逐个终止残留进程
            if (process.platform === 'win32') {
                for (const proc of remainingProcesses) {
                    if (proc.pid) {
                        try {
                            console.log(`[INFO] 终止进程 PID: ${proc.pid}`);
                            execSync(`taskkill /F /PID ${proc.pid}`, { stdio: 'ignore' });
                        } catch (error) {
                            console.log(`[WARNING] 无法终止进程 ${proc.pid}: ${error.message}`);
                        }
                    }
                }
            }

            retryCount++;
            if (retryCount < maxRetries) {
                console.log(`[INFO] 等待重试 (${retryCount}/${maxRetries})...`);
                await sleep(2000);
            }
        }

        // 第三步：最后的强力终止尝试
        if (process.platform === 'win32') {
            const finalCheck = getAllVSCodeProcesses();
            if (finalCheck.length > 0) {
                console.log('[INFO] 尝试最强力的终止方式 (wmic)...');
                try {
                    execSync('wmic process where "name=\'Code.exe\'" delete', { stdio: 'ignore' });
                    await sleep(2000);

                    const ultimateCheck = getAllVSCodeProcesses();
                    if (ultimateCheck.length === 0) {
                        console.log('[SUCCESS] 强力终止成功');
                        return true;
                    } else {
                        console.log(`[WARNING] 仍有 ${ultimateCheck.length} 个进程无法终止`);
                        ultimateCheck.forEach(proc => {
                            console.log(`          顽固进程 PID: ${proc.pid}`);
                        });
                    }
                } catch (error) {
                    console.log(`[WARNING] wmic 终止失败: ${error.message}`);
                }
            }
        }

        return getAllVSCodeProcesses().length === 0;
    } catch (error) {
        console.error(`[ERROR] 终止进程失败: ${error.message}`);
        return false;
    }
}

/**
 * 强制删除文件（Windows专用，多种方法）
 * @param {string} filePath - 文件路径
 * @returns {boolean} - 是否成功删除
 */
function forceDeleteFile(filePath) {
    if (process.platform !== 'win32') {
        return false;
    }

    const methods = [
        {
            name: 'del命令',
            command: `del /F /Q "${filePath}"`
        },
        {
            name: 'PowerShell Remove-Item',
            command: `powershell -Command "Remove-Item -Path '${filePath}' -Force -ErrorAction SilentlyContinue"`
        },
        {
            name: 'attrib + del',
            command: `attrib -R -S -H "${filePath}" && del /F /Q "${filePath}"`
        },
        {
            name: 'PowerShell强制解锁删除',
            command: `powershell -Command "try { [System.IO.File]::Delete('${filePath}') } catch { Write-Host 'Failed' }"`
        },
        {
            name: 'robocopy清空',
            command: `robocopy "${path.dirname(filePath)}" "${path.dirname(filePath)}" /PURGE /XF "${path.basename(filePath)}"`
        }
    ];

    for (const method of methods) {
        try {
            console.log(`[INFO] 尝试使用 ${method.name} 删除文件...`);
            execSync(method.command, { stdio: 'ignore' });

            // 检查文件是否真的被删除了
            if (!fs.existsSync(filePath)) {
                console.log(`[SUCCESS] 使用 ${method.name} 强制删除成功: ${filePath}`);
                return true;
            }
        } catch (error) {
            console.log(`[WARNING] ${method.name} 失败: ${error.message}`);
        }
    }

    // 最后的尝试：使用Windows内置的sdelete（如果可用）
    try {
        console.log(`[INFO] 尝试使用 sdelete 安全删除...`);
        execSync(`sdelete -p 1 -s -z "${filePath}"`, { stdio: 'ignore' });
        if (!fs.existsSync(filePath)) {
            console.log(`[SUCCESS] 使用 sdelete 删除成功: ${filePath}`);
            return true;
        }
    } catch (error) {
        // sdelete可能不可用，忽略错误
    }

    console.error(`[ERROR] 所有强制删除方法都失败了: ${filePath}`);
    return false;
}

/**
 * 查找并终止占用文件的进程
 * @param {string} filePath - 文件路径
 * @returns {Promise<boolean>} - 是否成功释放文件
 */
async function killProcessesUsingFile(filePath) {
    if (process.platform !== 'win32') {
        return false;
    }

    try {
        console.log(`[INFO] 查找占用文件的进程: ${filePath}`);

        // 使用handle.exe查找占用文件的进程（如果可用）
        try {
            const handleResult = execSync(`handle.exe "${filePath}"`, { encoding: 'utf8' });
            const lines = handleResult.split('\n').filter(line => line.includes('.exe'));

            for (const line of lines) {
                const pidMatch = line.match(/pid:\s*(\d+)/i);
                if (pidMatch) {
                    const pid = pidMatch[1];
                    try {
                        console.log(`[INFO] 终止占用文件的进程 PID: ${pid}`);
                        execSync(`taskkill /F /PID ${pid}`, { stdio: 'ignore' });
                    } catch (error) {
                        console.log(`[WARNING] 无法终止进程 ${pid}: ${error.message}`);
                    }
                }
            }
            return true;
        } catch (error) {
            // handle.exe可能不可用
        }

        // 使用PowerShell查找占用文件的进程
        try {
            const psScript = `
                $file = "${filePath.replace(/\\/g, '\\\\')}"
                Get-Process | Where-Object {
                    try {
                        $_.Modules | Where-Object { $_.FileName -eq $file }
                    } catch { }
                } | ForEach-Object {
                    Write-Host "PID:$($_.Id)"
                }
            `;
            const psResult = execSync(`powershell -Command "${psScript}"`, { encoding: 'utf8' });
            const pids = psResult.match(/PID:(\d+)/g);

            if (pids) {
                for (const pidMatch of pids) {
                    const pid = pidMatch.replace('PID:', '');
                    try {
                        console.log(`[INFO] 终止占用文件的进程 PID: ${pid}`);
                        execSync(`taskkill /F /PID ${pid}`, { stdio: 'ignore' });
                    } catch (error) {
                        console.log(`[WARNING] 无法终止进程 ${pid}: ${error.message}`);
                    }
                }
                return true;
            }
        } catch (error) {
            // PowerShell查找失败
        }

        return false;
    } catch (error) {
        console.log(`[WARNING] 查找占用进程失败: ${error.message}`);
        return false;
    }
}

/**
 * 安全删除文件（增强版，带重试机制）
 * @param {string} filePath - 文件路径
 * @param {boolean} forceMode - 是否启用强制模式
 * @returns {Promise<boolean>} - 是否成功删除
 */
async function safeDeleteFile(filePath, forceMode = false) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`[INFO] 文件不存在: ${filePath}`);
            return false;
        }

        console.log(`[INFO] 尝试删除: ${filePath}`);

        // 第一步：尝试正常删除
        try {
            fs.unlinkSync(filePath);
            console.log(`[SUCCESS] 已删除: ${filePath}`);
            return true;
        } catch (error) {
            if (error.code === 'EBUSY' || error.code === 'EPERM' || error.code === 'EACCES') {
                console.error(`[ERROR] 删除失败: ${filePath}`);
                console.error(`        错误: ${error.code} - ${error.message}`);

                if (forceMode) {
                    console.log(`[INFO] 启用强制模式，等待后重试...`);

                    // 等待一下，让可能的文件锁释放
                    await sleep(1000);

                    // 第二步：再次尝试正常删除
                    try {
                        fs.unlinkSync(filePath);
                        console.log(`[SUCCESS] 延迟删除成功: ${filePath}`);
                        return true;
                    } catch (retryError) {
                        console.log(`[INFO] 正常删除仍然失败，尝试查找并终止占用进程...`);

                        // 第三步：查找并终止占用文件的进程
                        if (process.platform === 'win32') {
                            await killProcessesUsingFile(filePath);
                            await sleep(2000); // 等待进程终止

                            // 再次尝试删除
                            try {
                                fs.unlinkSync(filePath);
                                console.log(`[SUCCESS] 终止占用进程后删除成功: ${filePath}`);
                                return true;
                            } catch (stillFailedError) {
                                console.log(`[INFO] 仍然失败，尝试强制删除方法...`);
                            }
                        }

                        // 第四步：使用强制删除
                        if (process.platform === 'win32') {
                            const forceResult = forceDeleteFile(filePath);
                            if (forceResult) {
                                return true;
                            }

                            // 第五步：最后的尝试 - 等待更长时间后再试
                            console.log(`[INFO] 等待5秒后最后一次尝试...`);
                            await sleep(5000);

                            try {
                                fs.unlinkSync(filePath);
                                console.log(`[SUCCESS] 最终删除成功: ${filePath}`);
                                return true;
                            } catch (finalError) {
                                console.error(`[ERROR] 最终删除失败: ${filePath}`);
                                console.error(`        最终错误: ${finalError.message}`);
                                console.log(`[TIP] 文件可能被系统进程锁定，建议重启后再试`);
                                return false;
                            }
                        }
                    }
                } else {
                    console.log(`[TIP] 可以尝试使用 --force 参数强制删除`);
                    console.log(`[TIP] 或者使用 --kill-processes 参数先终止VSCode进程`);
                    return false;
                }
            } else {
                throw error;
            }
        }
    } catch (error) {
        console.error(`[ERROR] 删除失败: ${filePath}`);
        console.error(`        错误: ${error.message}`);
        return false;
    }
}

/**
 * 删除globalStorage中的状态文件
 * @param {boolean} forceMode - 是否启用强制模式
 */
async function cleanGlobalStorage(forceMode = false) {
    console.log('\n[INFO] 清理 globalStorage...');
    const globalStoragePath = path.join(codeUserPath, 'globalStorage');

    if (!fs.existsSync(globalStoragePath)) {
        console.log(`[ERROR] globalStorage 目录不存在: ${globalStoragePath}`);
        return;
    }

    let deletedCount = 0;
    for (const fileName of TARGET_FILES) {
        const filePath = path.join(globalStoragePath, fileName);
        if (await safeDeleteFile(filePath, forceMode)) {
            deletedCount++;
        }
    }

    console.log(`[SUMMARY] globalStorage 清理完成，删除了 ${deletedCount} 个文件`);
}

/**
 * 递归搜索并删除workspaceStorage中的状态文件
 * @param {boolean} forceMode - 是否启用强制模式
 */
async function cleanWorkspaceStorage(forceMode = false) {
    console.log('\n[INFO] 清理 workspaceStorage...');
    const workspaceStoragePath = path.join(codeUserPath, 'workspaceStorage');

    if (!fs.existsSync(workspaceStoragePath)) {
        console.log(`[ERROR] workspaceStorage 目录不存在: ${workspaceStoragePath}`);
        return;
    }

    let totalDeleted = 0;
    let workspacesProcessed = 0;

    try {
        const workspaceDirs = fs.readdirSync(workspaceStoragePath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

        console.log(`[INFO] 找到 ${workspaceDirs.length} 个工作区目录`);

        for (const workspaceDir of workspaceDirs) {
            const workspacePath = path.join(workspaceStoragePath, workspaceDir);
            console.log(`\n  [INFO] 检查工作区: ${workspaceDir}`);

            let deletedInWorkspace = 0;
            for (const fileName of TARGET_FILES) {
                const filePath = path.join(workspacePath, fileName);
                if (await safeDeleteFile(filePath, forceMode)) {
                    deletedInWorkspace++;
                    totalDeleted++;
                }
            }

            if (deletedInWorkspace > 0) {
                workspacesProcessed++;
            }
        }

    } catch (error) {
        console.error(`[ERROR] 读取 workspaceStorage 目录失败: ${error.message}`);
        return;
    }

    console.log(`[SUMMARY] workspaceStorage 清理完成:`);
    console.log(`          - 处理了 ${workspacesProcessed} 个工作区`);
    console.log(`          - 删除了 ${totalDeleted} 个文件`);
}

/**
 * 显示警告信息和使用说明
 */
function showWarning() {
    console.log('[WARNING] 此操作将删除VSCode的状态数据库文件');
    console.log('          这可能会导致VSCode丢失以下数据:');
    console.log('          - 扩展的状态和配置');
    console.log('          - 已保存的认证信息');
    console.log('          - 窗口布局和位置');
    console.log('          - 打开的文件列表');
    console.log('');
    console.log('[TIP] 建议在运行此脚本前关闭所有VSCode实例');
    console.log('');
    console.log('[USAGE] 可用参数:');
    console.log('        --force, -f              强制删除被锁定的文件');
    console.log('        --kill-processes, -k     自动终止所有VSCode进程');
    console.log('        --skip-process-check     跳过VSCode进程检查');
    console.log('        --aggressive, -a         激进模式（自动终止进程+强制删除）');
    console.log('');
}

/**
 * 主函数
 */
async function main() {
    console.log('VSCode State Database Cleaner');
    console.log('================================');

    // 解析命令行参数
    const args = process.argv.slice(2);
    const aggressiveMode = args.includes('--aggressive') || args.includes('-a');
    const forceMode = args.includes('--force') || args.includes('-f') || aggressiveMode;
    const killProcesses = args.includes('--kill-processes') || args.includes('-k') || aggressiveMode;
    const skipProcessCheck = args.includes('--skip-process-check');

    if (aggressiveMode) {
        console.log('[INFO] 激进模式已启用（自动终止进程+强制删除）');
    } else {
        if (forceMode) {
            console.log('[INFO] 强制模式已启用');
        }
        if (killProcesses) {
            console.log('[INFO] 自动终止进程模式已启用');
        }
    }

    showWarning();

    // 检查并处理VSCode进程（除非跳过检查）
    if (!skipProcessCheck) {
        console.log('[INFO] 检查VSCode进程...');
        if (checkVSCodeProcesses()) {
            console.log('[WARNING] 检测到VSCode进程仍在运行!');

            if (killProcesses) {
                // 自动终止进程
                const killSuccess = await killAllVSCodeProcesses();
                if (!killSuccess) {
                    console.log('[ERROR] 无法终止所有VSCode进程，请手动关闭后重试');
                    if (!forceMode) {
                        process.exit(1);
                    }
                }
            } else {
                console.log('          建议先完全关闭VSCode，然后重新运行此脚本');
                console.log('          或者使用 --kill-processes 参数自动终止进程');
                console.log('          或者使用 --skip-process-check 参数跳过此检查');
                if (!forceMode) {
                    console.log('          或者使用 --force 参数强制删除');
                    process.exit(1);
                }
            }
        } else {
            console.log('[INFO] 未检测到VSCode进程');
        }
    }

    // 检查VSCode User目录是否存在
    if (!fs.existsSync(codeUserPath)) {
        console.error(`[ERROR] VSCode User 目录不存在: ${codeUserPath}`);
        console.error('        请确认VSCode已安装并至少运行过一次');
        process.exit(1);
    }

    console.log(`[INFO] VSCode User 目录: ${codeUserPath}`);

    // 执行清理
    await cleanGlobalStorage(forceMode);
    await cleanWorkspaceStorage(forceMode);

    console.log('\n[COMPLETE] 清理完成!');
    console.log('[TIP] 重新启动VSCode以应用更改');
}

// 运行脚本
if (require.main === module) {
    main().catch(error => {
        console.error('[ERROR] 脚本执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = {
    cleanGlobalStorage,
    cleanWorkspaceStorage,
    safeDeleteFile,
    checkVSCodeProcesses,
    forceDeleteFile,
    listVSCodeProcesses,
    killAllVSCodeProcesses,
    killProcessesUsingFile
};
